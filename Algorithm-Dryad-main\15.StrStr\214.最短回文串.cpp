/*
 * @lc app=leetcode.cn id=214 lang=cpp
 *
 * [214] 最短回文串
 *
 * https://leetcode-cn.com/problems/shortest-palindrome/description/
 *
 * algorithms
 * Hard (36.75%)
 * Likes:    336
 * Dislikes: 0
 * Total Accepted:    27.3K
 * Total Submissions: 74.4K
 * Testcase Example:  '"aacecaaa"'
 *
 * 给定一个字符串 s，你可以通过在字符串前面添加字符将其转换为回文串。找到并返回可以用这种方式转换的最短回文串。
 * 
 * 
 * 
 * 示例 1：
 * 
 * 
 * 输入：s = "aacecaaa"
 * 输出："aaacecaaa"
 * 
 * 
 * 示例 2：
 * 
 * 
 * 输入：s = "abcd"
 * 输出："dcbabcd"
 * 
 * 
 * 
 * 
 * 提示：
 * 
 * 
 * 0 
 * s 仅由小写英文字母组成
 * 
 * 
 */

// @lc code=start
class Solution {
public:
    string shortestPalindrome(string s) {

    }
};
// @lc code=end

