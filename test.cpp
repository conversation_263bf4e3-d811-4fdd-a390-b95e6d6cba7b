#include<iostream>
#include<stack>
using namespace std;
class Solution {
public:
    bool isValid(string s) {
        if (s.size()==0)return true;
        if (s.size()%2==1)return false;
        stack <char> st;
        for (int i=0;i<s.size();i++){
            if (s[i]=='('||s[i]=='{'||s[i]=='['){
                st.push(s[i]);
            }
            if(s[i]==')'||s[i]=='}'||s[i]==']'){
                if (st.empty()) return false;
                char top = st.top();
                st.pop();
                if ((s[i]==')'&&top!='(')||(s[i]=='}'&&top!='{')||(s[i]==']'&&top!='[')) return false;
            }
        }
        return st.empty();
    }
    bool fish()
};
int main()
{
    Solution s;
    // string s1="()";
    // s.isValid("()[]{}");
    cout<<s.isValid("()[]{}")<<endl;
    return 0;
}

