# 最小生成树的代价

## 题目

```
给定点集和边集，求最小生成树的代价，如果最后不能生成最小生成树，那么返回MAX_INT。
```

## 思路

首先利用Kruskal算法，在生成最小生成树之后，还需要加一道检测，那就是，看一下是不是所有的点
都放到了最小生成树中。

如果没有放到同一个树中，那么肯定不是符合要求的。需要返回MAX_INT。
否则那么应该直接返回代价。

## 代码

### Java

```Java
//测试平台 http://poj.org/problem?id=1287
import java.io.*;
import java.util.*;

// your code here
class Solution {
  private int count = 0;
  private long cost = 0;
  // 这里直接申请了足够多的内存
  private int[] F = new int[3000];
  // 并查集初始化
  // 注意点的编号是从1开始
  private void Init(int n) {
    for (int i = 0; i <= n; i++) {
      F[i] = i;
    }
    cost = 0;
    count = n;
  }

  private int Find(int x) {
    if (x == F[x]) {
      return x;
    }
    F[x] = Find(F[x]);
    return F[x];
  }

  // 在合并的时候，需要加上代价
  private void Union(int x, int y, int pay) {
    if (Find(x) != Find(y)) {
     cost += pay;
     count--;
    }
    F[Find(x)] = Find(y);
  }

  // 一共有n个点，编号从1~n
  // conn表示输入的边的集合
  // 每一项是一个三元组[点a, 点b, 需要费用c]
  public long Kruskal(int n, int m, int[][] conn) {
    Init(n);

    Arrays.sort(conn, 0, m, new Comparator<int[]>() {
      public int compare(int[] a, int[] b) {
        return a[2] - b[2];
      }
    });

    for (int i = 0; i < m; i++) {
      Union(conn[i][0], conn[i][1], conn[i][2]);
    }

    return count <= 1 ? cost : Integer.MAX_VALUE;
  }
}


// Test code
public class Main {
  static private int pointsNumber = 0;
  static private int[][] conn = new int[7000][3];
  static private Solution s = new Solution();
  public static void main(String args[]) throws Exception {
    Scanner sc = new Scanner(System.in);

    int testCase = 0;

    while (sc.hasNext()) {
      // 点的数目
      int n = sc.nextInt();
      if (n == 0) {
        break;
      }
      // 边的数目
      int m = sc.nextInt();

      for (int i = 0; i < m; i++) {
        // 连接的点a
        conn[i][0] = sc.nextInt();
        // 连接的点b
        conn[i][1] = sc.nextInt();
        // 权重
        conn[i][2] = sc.nextInt();
      }

      System.out.println(s.Kruskal(n, m, conn));
    }
  }
}
```

### Python

```Python
class UF(object):
    def __init__(self, N):
        self.F = [0] * N
        for i in range(N):
            self.F[i] = i
        self.totalCost = 0

    def Find(self, x):
        if x == self.F[x]:
            return x
        self.F[x] = self.Find(self.F[x])
        return self.F[x]

    def Union(self, x, y, cost):
        xpar = self.Find(x)
        ypar = self.Find(y)
        if xpar != ypar:
            self.F[xpar] = ypar
            self.totalCost += cost

class MST(object):
    def solve(self, N, es):
        es = sorted(es, key=(lambda x:x[2]))

        uf = UF(N + 1)
        for e in es:
            uf.Union(e[0], e[1], e[2])

        for e in es:
            if uf.Find(e[0]) != uf.Find(e[1]):
                return float('inf')

        return uf.totalCost

```

### Cpp

```Cpp

//测试平台 http://poj.org/problem?id=1287

#include <limits.h>
#include <assert.h>
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <stddef.h>
#include <stdint.h>

#define N 500

typedef struct _edge
{
  int in;
  int out;
  int weight;
} edge;

edge ES[N];

int F[N];

int
find(int x)
{
  return x != F[x] ? F[x] = find(F[x]) : F[x];
}

int
join(int x, int y)
{
  int px = find(x);
  int py = find(y);
  if (px == py)
    return 0;
  F[px] = py;
  return 1;
}

int
cmp(const void* a, const void* b)
{
  const edge* x = (const edge*)a;
  const edge* y = (const edge*)b;
  if (x->weight < y->weight)
    return -1;
  if (x->weight == y->weight)
    return 0;
  return 1;
}

int
main(void)
{
  int P;
  int R;
  int i = 0;
  int ans = 0;

  while (scanf("%d", &P) != EOF && P > 0) {
    scanf("%d", &R);

    if (R == 0) {
      printf("0\n");
      continue;
    }

    for (i = 0; i < R; i++) {
      scanf("%d%d%d", &ES[i].in, &ES[i].out, &ES[i].weight);
      F[ES[i].in] = ES[i].in;
      F[ES[i].out] = ES[i].out;
    }

    qsort(ES, R, sizeof(*ES), cmp);

    ans = 0;
    for (i = 0; i < R; i++) {
      if (join(ES[i].in, ES[i].out)) {
        ans += ES[i].weight;
      }
    }

    // 检查是不是都到一个集合里面了
    for (int i = 0; i < R; i++) {
      if (find(ES[i].in) != find(ES[i].out)) {
        printf("%d\n", INT_MAX);
      }
    }

    printf("%d\n", ans);
  }
  return 0;
}

```
