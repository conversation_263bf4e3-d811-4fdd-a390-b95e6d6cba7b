import java.util.ArrayList;

/*
 *
 * [144] 二叉树的前序遍历
 *
 * https://leetcode-cn.com/problems/binary-tree-preorder-traversal/description/
 * https://www.lintcode.com/problem/binary-tree-preorder-traversal/
 *
 * algorithms
 * Medium (66.89%)
 * Likes:    367
 * Dislikes: 0
 * Total Accepted:    170.7K
 * Total Submissions: 255.3K
 * Testcase Example:  '[1,null,2,3]'
 *
 * 给定一个二叉树，返回它的 前序 遍历。
 * 
 * 示例:
 * 
 * 输入: [1,null,2,3]  
 * ⁠  1
 * ⁠   \
 * ⁠    2
 * ⁠   /
 * ⁠  3 
 * 
 * 输出: [1,2,3]
 * 
 * 
 * 进阶: 递归算法很简单，你可以通过迭代算法完成吗？
 * 
 */

import java.util.List;
import java.util.Stack;
import javax.swing.tree.TreeNode;

/**
 * Definition for a binary tree node. public class TreeNode { int val; TreeNode
 * left; TreeNode right; TreeNode(int x) { val = x; } }
 */
class Solution {
    public List<Integer> preorderTraversal(TreeNode root) {
        Stack<TreeNode> s = new Stack<>();
        List<Integer> ans = new ArrayList<>();
        while (root != null || !s.empty()) {
            while (root != null) {
                ans.add(root.val);
                s.push(root);
                root = root.left;
            }
            root = s.peek();
            s.pop();
            root = root.right;
        }
        return ans;
    }
}
